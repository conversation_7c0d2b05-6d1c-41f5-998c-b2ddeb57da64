# -*- coding: utf-8 -*-
'''
2025/07/05 Ka<PERSON>ga
Scroll bar color modified for better visibility
CSV file based batch object replacement tool for instances or references.
Very effective when specifications are finalized.
Wildcard keyword replacement is also supported.



'''

import csv
import os
import re
from pymxs import runtime as rt
from PySide2.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout, 
                             QHBoxLayout, QLabel, QLineEdit, QPushButton, 
                             QTableWidget, QTableWidgetItem, QRadioButton, 
                             QButtonGroup, QFileDialog, QMessageBox, QHeaderView,
                             QGroupBox, QCheckBox, QSpinBox)
from PySide2.QtCore import Qt
from PySide2.QtGui import QIcon

class ObjectReplacer(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("Object Replacer Tool")
        self.setGeometry(100, 100, 700, 700)
        
        # メインウィジェット
        main_widget = QWidget()
        self.setCentralWidget(main_widget)
        
        # メインレイアウト
        main_layout = QVBoxLayout(main_widget)
        
        # CSV file path input area
        csv_layout = QHBoxLayout()
        csv_layout.addWidget(QLabel("CSV File Path:"))

        self.csv_path_edit = QLineEdit()
        csv_layout.addWidget(self.csv_path_edit)

        browse_btn = QPushButton("Browse...")
        browse_btn.clicked.connect(self.browse_csv)
        csv_layout.addWidget(browse_btn)

        main_layout.addLayout(csv_layout)

        # CSV loading options
        csv_options_layout = QHBoxLayout()
        csv_options_layout.addWidget(QLabel("Load Options:"))

        self.skip_header_check = QCheckBox("Skip Header Row")
        self.skip_header_check.setChecked(True)
        self.skip_header_check.setToolTip("Check if first row contains headers")
        csv_options_layout.addWidget(self.skip_header_check)

        csv_options_layout.addWidget(QLabel("Skip Rows:"))
        self.skip_rows_spinbox = QSpinBox()
        self.skip_rows_spinbox.setRange(0, 10)
        self.skip_rows_spinbox.setValue(0)
        self.skip_rows_spinbox.setToolTip("Number of rows to skip from top")
        csv_options_layout.addWidget(self.skip_rows_spinbox)
        
        csv_options_layout.addStretch()
        main_layout.addLayout(csv_options_layout)
        
        # Replacement options
        options_layout = QHBoxLayout()
        options_layout.addWidget(QLabel("Replacement Type:"))

        self.button_group = QButtonGroup()

        self.instance_radio = QRadioButton("Instance")
        self.instance_radio.setChecked(True)
        self.button_group.addButton(self.instance_radio)
        options_layout.addWidget(self.instance_radio)

        self.reference_radio = QRadioButton("Reference")
        self.button_group.addButton(self.reference_radio)
        options_layout.addWidget(self.reference_radio)
        
        options_layout.addStretch()
        main_layout.addLayout(options_layout)
        
        # String manipulation area
        wildcard_group = QGroupBox("String Operations")
        wildcard_layout = QVBoxLayout(wildcard_group)

        # Prefix/Suffix settings
        prefix_suffix_layout = QHBoxLayout()

        # Prefix
        prefix_layout = QVBoxLayout()
        prefix_layout.addWidget(QLabel("Prefix:"))
        self.prefix_edit = QLineEdit()
        self.prefix_edit.setPlaceholderText("Prefix to add")
        prefix_layout.addWidget(self.prefix_edit)

        # Suffix
        suffix_layout = QVBoxLayout()
        suffix_layout.addWidget(QLabel("Suffix:"))
        self.suffix_edit = QLineEdit()
        self.suffix_edit.setPlaceholderText("Suffix to add")
        suffix_layout.addWidget(self.suffix_edit)
        
        prefix_suffix_layout.addLayout(prefix_layout)
        prefix_suffix_layout.addLayout(suffix_layout)
        
        wildcard_layout.addLayout(prefix_suffix_layout)
        
        # Remove word settings
        remove_word_layout = QHBoxLayout()
        remove_word_layout.addWidget(QLabel("Remove Word:"))
        self.remove_word_edit = QLineEdit()
        self.remove_word_edit.setPlaceholderText("String to remove")
        remove_word_layout.addWidget(self.remove_word_edit)
        wildcard_layout.addLayout(remove_word_layout)

        # Apply target selection
        target_layout = QHBoxLayout()
        target_layout.addWidget(QLabel("Apply To:"))

        self.apply_to_source_check = QCheckBox("Source")
        self.apply_to_source_check.setChecked(True)
        target_layout.addWidget(self.apply_to_source_check)

        self.apply_to_target_check = QCheckBox("Target")
        self.apply_to_target_check.setChecked(False)
        target_layout.addWidget(self.apply_to_target_check)
        
        target_layout.addStretch()
        wildcard_layout.addLayout(target_layout)
        
        # Apply buttons
        button_layout = QHBoxLayout()
        apply_prefix_suffix_btn = QPushButton("Apply Prefix/Suffix")
        apply_prefix_suffix_btn.clicked.connect(self.apply_prefix_suffix)
        button_layout.addWidget(apply_prefix_suffix_btn)

        remove_word_btn = QPushButton("Remove Word")
        remove_word_btn.clicked.connect(self.remove_word)
        button_layout.addWidget(remove_word_btn)
        
        wildcard_layout.addLayout(button_layout)
        
        main_layout.addWidget(wildcard_group)
        
        # Table widget
        self.table_widget = QTableWidget()
        self.table_widget.setColumnCount(3)
        self.table_widget.setHorizontalHeaderLabels(["Source Object", "Target Object", "Status"])
        
        # スクロールバーのスタイルを設定（明るい色に変更）
        scrollbar_style = """
            QScrollBar:vertical {
                background-color: #474747;
                border: 1px solid #d0d0d0;
                border-radius: 6px;
                width: 16px;
                margin: 0px;
            }
            
            QScrollBar::handle:vertical {
                background-color: #e3e3e3;
                border-radius: 6px;
                min-height: 20px;
                margin: 2px;
            }
            
            QScrollBar::handle:vertical:hover {
                background-color: #106ebe;
            }
            
            QScrollBar::handle:vertical:pressed {
                background-color: #005a9e;
            }
            
            QScrollBar::add-line:vertical,
            QScrollBar::sub-line:vertical {
                background: none;
                border: none;
                height: 0px;
            }
            
            QScrollBar::add-page:vertical,
            QScrollBar::sub-page:vertical {
                background: none;
            }
            
            QScrollBar:horizontal {
                background-color: #f0f0f0;
                border: 1px solid #d0d0d0;
                border-radius: 6px;
                height: 16px;
                margin: 0px;
            }
            
            QScrollBar::handle:horizontal {
                background-color: #0078d4;
                border-radius: 6px;
                min-width: 20px;
                margin: 2px;
            }
            
            QScrollBar::handle:horizontal:hover {
                background-color: #106ebe;
            }
            
            QScrollBar::handle:horizontal:pressed {
                background-color: #005a9e;
            }
            
            QScrollBar::add-line:horizontal,
            QScrollBar::sub-line:horizontal {
                background: none;
                border: none;
                width: 0px;
            }
            
            QScrollBar::add-page:horizontal,
            QScrollBar::sub-page:horizontal {
                background: none;
            }
        """
        
        self.table_widget.setStyleSheet(scrollbar_style)
        
        # テーブルの編集設定（状態列以外は編集可能）
        self.table_widget.itemChanged.connect(self.on_item_changed)
        
        # 複数選択を有効にする
        self.table_widget.setSelectionBehavior(QTableWidget.SelectRows)
        self.table_widget.setSelectionMode(QTableWidget.MultiSelection)
        
        # カラム幅を調整
        header = self.table_widget.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.Stretch)
        header.setSectionResizeMode(1, QHeaderView.Stretch)
        header.setSectionResizeMode(2, QHeaderView.ResizeToContents)
        
        main_layout.addWidget(self.table_widget)
        
        # Help information
        help_layout = QHBoxLayout()
        help_label = QLabel("Help: Wildcards (*) can be used in object names. Example: 'Box*' matches 'Box001', 'Box_red', etc.")
        help_label.setStyleSheet("color: white; font-size: 10px;")
        help_layout.addWidget(help_label)
        help_layout.addStretch()
        main_layout.addLayout(help_layout)

        # Button area
        buttons_layout = QHBoxLayout()

        load_btn = QPushButton("Load CSV")
        load_btn.clicked.connect(self.load_csv)
        buttons_layout.addWidget(load_btn)

        save_btn = QPushButton("Save CSV")
        save_btn.clicked.connect(self.save_csv)
        buttons_layout.addWidget(save_btn)

        add_row_btn = QPushButton("Add Row")
        add_row_btn.clicked.connect(self.add_row)
        buttons_layout.addWidget(add_row_btn)

        delete_row_btn = QPushButton("Delete Row")
        delete_row_btn.clicked.connect(self.delete_row)
        buttons_layout.addWidget(delete_row_btn)

        replace_btn = QPushButton("Execute Replace")
        replace_btn.clicked.connect(self.replace_objects)
        buttons_layout.addWidget(replace_btn)

        close_btn = QPushButton("Close")
        close_btn.clicked.connect(self.close_window)
        buttons_layout.addWidget(close_btn)
        
        main_layout.addLayout(buttons_layout)
        
    def browse_csv(self):
        """Display dialog to select CSV file"""
        file_path, _ = QFileDialog.getOpenFileName(
            self,
            "Select CSV File",
            "",
            "CSV Files (*.csv);;All Files (*)"
        )
        
        if file_path:
            self.csv_path_edit.setText(file_path)
            
    def load_csv(self):
        """Load CSV file and display in table"""
        csv_path = self.csv_path_edit.text().strip()

        if not csv_path:
            QMessageBox.warning(self, "Error", "Please specify a CSV file")
            return

        if not os.path.exists(csv_path):
            QMessageBox.warning(self, "Error", "The specified CSV file does not exist")
            return
            
        # テーブルをクリア
        self.table_widget.setRowCount(0)
        
        # 読み込みオプションを取得
        skip_header = self.skip_header_check.isChecked()
        skip_rows = self.skip_rows_spinbox.value()
        
        # 複数のエンコーディングを試す
        encodings = ['utf-8', 'shift-jis', 'cp932', 'utf-8-sig']
        
        for encoding in encodings:
            try:
                with open(csv_path, 'r', encoding=encoding) as f:
                    reader = csv.reader(f)
                    
                    # 全ての行を読み込む
                    all_rows = list(reader)
                    
                    # スキップする行数を計算
                    total_skip = skip_rows
                    if skip_header:
                        total_skip += 1
                    
                    # スキップした行を除外
                    data_rows = all_rows[total_skip:]
                    
                    # テーブルに追加
                    table_row_index = 0
                    for row in data_rows:
                        if len(row) >= 2:
                            # 新しい行を追加
                            self.table_widget.insertRow(table_row_index)
                            
                            # Set data
                            source_item = QTableWidgetItem(row[0].strip())
                            target_item = QTableWidgetItem(row[1].strip())
                            status_item = QTableWidgetItem("Pending")

                            # Make status column non-editable
                            status_item.setFlags(status_item.flags() & ~Qt.ItemIsEditable)
                            
                            self.table_widget.setItem(table_row_index, 0, source_item)
                            self.table_widget.setItem(table_row_index, 1, target_item)
                            self.table_widget.setItem(table_row_index, 2, status_item)
                            
                            table_row_index += 1
                
                # Load completion message
                skipped_info = []
                if skip_header:
                    skipped_info.append("header row")
                if skip_rows > 0:
                    skipped_info.append(f"{skip_rows} rows")

                skip_message = ""
                if skipped_info:
                    skip_message = f"\nSkipped: {', '.join(skipped_info)}"

                QMessageBox.information(
                    self,
                    "Complete",
                    f"Loaded {self.table_widget.rowCount()} data items\n"
                    f"Encoding: {encoding}{skip_message}"
                )
                return  # Exit on success

            except UnicodeDecodeError:
                # If this encoding doesn't work, try the next one
                continue
            except Exception as e:
                # Log other errors and try the next encoding
                print(f"Error with encoding {encoding}: {str(e)}")
                continue

        # If all encodings failed
        QMessageBox.critical(self, "Error", "Failed to load CSV\nSupported encodings: UTF-8, Shift-JIS, CP932")
    
    def save_csv(self):
        """Save table contents to CSV file"""
        if self.table_widget.rowCount() == 0:
            QMessageBox.warning(self, "Error", "No data to save")
            return

        file_path, _ = QFileDialog.getSaveFileName(
            self,
            "Save CSV File",
            "",
            "CSV Files (*.csv);;All Files (*)"
        )

        if not file_path:
            return

        try:
            with open(file_path, 'w', newline='', encoding='utf-8-sig') as f:
                writer = csv.writer(f)

                # Write header (optional)
                # writer.writerow(["Source", "Target"])

                # Write data
                for row in range(self.table_widget.rowCount()):
                    source_item = self.table_widget.item(row, 0)
                    target_item = self.table_widget.item(row, 1)

                    if source_item and target_item:
                        writer.writerow([source_item.text(), target_item.text()])

            QMessageBox.information(self, "Complete", f"CSV file saved\n{file_path}")

        except Exception as e:
            QMessageBox.critical(self, "Error", f"Failed to save CSV file\n{str(e)}")
    
    def remove_word(self):
        """Remove specified word (selected rows only)"""
        remove_word = self.remove_word_edit.text().strip()

        if not remove_word:
            QMessageBox.warning(self, "Error", "Please enter a word to remove")
            return

        # Check apply targets
        apply_to_source = self.apply_to_source_check.isChecked()
        apply_to_target = self.apply_to_target_check.isChecked()

        if not apply_to_source and not apply_to_target:
            QMessageBox.warning(self, "Error", "Please select apply targets")
            return

        # Get selected rows
        selected_rows = set()
        for item in self.table_widget.selectedItems():
            selected_rows.add(item.row())

        if not selected_rows:
            QMessageBox.warning(self, "Error", "Please select rows to remove word from")
            return
            
        updated_count = 0

        for row in selected_rows:
            # Remove from source
            if apply_to_source:
                source_item = self.table_widget.item(row, 0)
                if source_item:
                    source_text = source_item.text()
                    new_text = source_text.replace(remove_word, '')

                    if new_text != source_text:
                        source_item.setText(new_text)
                        updated_count += 1

            # Remove from target
            if apply_to_target:
                target_item = self.table_widget.item(row, 1)
                if target_item:
                    target_text = target_item.text()
                    new_text = target_text.replace(remove_word, '')

                    if new_text != target_text:
                        target_item.setText(new_text)
                        updated_count += 1

        QMessageBox.information(self, "Complete", f"Removed word from {updated_count} items")
    
    def apply_prefix_suffix(self):
        """Apply prefix/suffix (selected rows only)"""
        prefix = self.prefix_edit.text().strip()
        suffix = self.suffix_edit.text().strip()

        if not prefix and not suffix:
            QMessageBox.warning(self, "Error", "Please enter prefix or suffix")
            return

        # Check apply targets
        apply_to_source = self.apply_to_source_check.isChecked()
        apply_to_target = self.apply_to_target_check.isChecked()

        if not apply_to_source and not apply_to_target:
            QMessageBox.warning(self, "Error", "Please select apply targets")
            return

        # Get selected rows
        selected_rows = set()
        for item in self.table_widget.selectedItems():
            selected_rows.add(item.row())

        if not selected_rows:
            QMessageBox.warning(self, "Error", "Please select rows to apply prefix/suffix to")
            return
            
        updated_count = 0

        for row in selected_rows:
            # Apply to source
            if apply_to_source:
                source_item = self.table_widget.item(row, 0)
                if source_item:
                    source_text = source_item.text()
                    new_text = source_text

                    # Add prefix
                    if prefix:
                        new_text = prefix + new_text

                    # Add suffix
                    if suffix:
                        new_text = new_text + suffix

                    if new_text != source_text:
                        source_item.setText(new_text)
                        updated_count += 1

            # Apply to target
            if apply_to_target:
                target_item = self.table_widget.item(row, 1)
                if target_item:
                    target_text = target_item.text()
                    new_text = target_text

                    # Add prefix
                    if prefix:
                        new_text = prefix + new_text

                    # Add suffix
                    if suffix:
                        new_text = new_text + suffix

                    if new_text != target_text:
                        target_item.setText(new_text)
                        updated_count += 1

        QMessageBox.information(self, "Complete", f"Updated {updated_count} items")
            
    def on_item_changed(self, item):
        """Process when table item is changed"""
        # Reset status of edited row to "Pending"
        row = item.row()
        if item.column() < 2:  # If source or target was changed
            status_item = QTableWidgetItem("Pending")
            status_item.setFlags(status_item.flags() & ~Qt.ItemIsEditable)
            self.table_widget.setItem(row, 2, status_item)
    
    def add_row(self):
        """Add new row"""
        row_count = self.table_widget.rowCount()
        self.table_widget.insertRow(row_count)

        # Create empty items
        source_item = QTableWidgetItem("")
        target_item = QTableWidgetItem("")
        status_item = QTableWidgetItem("Pending")
        status_item.setFlags(status_item.flags() & ~Qt.ItemIsEditable)

        self.table_widget.setItem(row_count, 0, source_item)
        self.table_widget.setItem(row_count, 1, target_item)
        self.table_widget.setItem(row_count, 2, status_item)

        # Set focus to new row
        self.table_widget.setCurrentCell(row_count, 0)

    def delete_row(self):
        """Delete selected row"""
        current_row = self.table_widget.currentRow()
        if current_row >= 0:
            reply = QMessageBox.question(
                self,
                "Confirm",
                "Delete selected row?",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )
            if reply == QMessageBox.Yes:
                self.table_widget.removeRow(current_row)
        else:
            QMessageBox.information(self, "Info", "Please select a row to delete")
    
    def wildcard_match(self, pattern, text):
        """Wildcard pattern matching"""
        # Convert pattern to regular expression
        # Convert * to .* and escape other regex special characters
        regex_pattern = re.escape(pattern).replace(r'\*', '.*')
        # Add ^ and $ for exact match
        regex_pattern = f'^{regex_pattern}$'

        try:
            return bool(re.match(regex_pattern, text, re.IGNORECASE))
        except re.error:
            # If regex error, compare with exact match
            return pattern.lower() == text.lower()

    def find_objects_by_pattern(self, pattern):
        """Search objects by pattern matching"""
        if '*' not in pattern:
            # If no wildcard, exact match
            obj = rt.getNodeByName(pattern)
            return [obj] if obj else []

        # If wildcard exists, pattern matching
        matching_objects = []
        try:
            for obj in rt.objects:
                if obj and hasattr(obj, 'name') and self.wildcard_match(pattern, obj.name):
                    matching_objects.append(obj)
        except Exception as e:
            print(f"Error during object search: {str(e)}")

        return matching_objects
    
    def replace_objects(self):
        """Execute object replacement"""
        if self.table_widget.rowCount() == 0:
            QMessageBox.warning(self, "Error", "No data to replace")
            return

        # Get replacement type
        use_instance = self.instance_radio.isChecked()

        success_count = 0

        for row in range(self.table_widget.rowCount()):
            source_name = self.table_widget.item(row, 0).text().strip()
            target_name = self.table_widget.item(row, 1).text().strip()

            try:
                # Search source objects (wildcard support)
                source_objects = self.find_objects_by_pattern(source_name)
                if not source_objects:
                    self.table_widget.setItem(row, 2, QTableWidgetItem("No Source"))
                    continue

                # Search target objects (wildcard support)
                target_objects = self.find_objects_by_pattern(target_name)
                if not target_objects:
                    self.table_widget.setItem(row, 2, QTableWidgetItem("No Target"))
                    continue

                # If multiple source objects, replace with first target
                target_obj = target_objects[0]
                replaced_count = 0

                for source_obj in source_objects:
                    # Execute replacement
                    if use_instance:
                        # Instance replacement
                        rt.instanceReplace(source_obj, target_obj)
                    else:
                        # Reference replacement
                        rt.referenceReplace(source_obj, target_obj)
                    replaced_count += 1

                if replaced_count > 1:
                    self.table_widget.setItem(row, 2, QTableWidgetItem(f"Success ({replaced_count} objects)"))
                else:
                    self.table_widget.setItem(row, 2, QTableWidgetItem("Success"))
                success_count += 1

            except Exception as e:
                self.table_widget.setItem(row, 2, QTableWidgetItem(f"Failed: {str(e)}"))

        # Completion message
        QMessageBox.information(
            self,
            "Complete",
            f"Replacement process completed\nSuccess: {success_count} / Total: {self.table_widget.rowCount()}"
        )
        
    def close_window(self):
        """Close window"""
        self.close()

def show_object_replacer():
    """Function to display object replacement tool"""
    # Check existing application instance
    app = QApplication.instance()
    if app is None:
        app = QApplication([])

    # Create and show tool window
    replacer = ObjectReplacer()
    replacer.show()

    return replacer

# Tool launch
# When running inside 3ds Max
if __name__ == "__main__":
    # Keep as global variable (to prevent garbage collection)
    global object_replacer_tool
    object_replacer_tool = show_object_replacer()
else:
    # When running as script
    show_object_replacer()