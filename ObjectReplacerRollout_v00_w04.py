
 
 i m p o r t   c s v 
 
 i m p o r t   o s 
 
 i m p o r t   r e 
 
 f r o m   p y m x s   i m p o r t   r u n t i m e   a s   r t 
 
 f r o m   P y S i d e 2 . Q t W i d g e t s   i m p o r t   ( Q A p p l i c a t i o n ,   Q M a i n W i n d o w ,   Q W i d g e t ,   Q V B o x L a y o u t ,   
 
                                                           Q H B o x L a y o u t ,   Q L a b e l ,   Q L i n e E d i t ,   Q P u s h B u t t o n ,   
 
                                                           Q T a b l e W i d g e t ,   Q T a b l e W i d g e t I t e m ,   Q R a d i o B u t t o n ,   
 
                                                           Q B u t t o n G r o u p ,   Q F i l e D i a l o g ,   Q M e s s a g e B o x ,   Q H e a d e r V i e w , 
 
                                                           Q G r o u p B o x ,   Q C h e c k B o x ,   Q S p i n B o x ) 
 
 f r o m   P y S i d e 2 . Q t C o r e   i m p o r t   Q t 
 
 f r o m   P y S i d e 2 . Q t G u i   i m p o r t   Q I c o n 
 
 
 
 c l a s s   O b j e c t R e p l a c e r ( Q M a i n W i n d o w ) : 
 
         d e f   _ _ i n i t _ _ ( s e l f ) : 
 
                 s u p e r ( ) . _ _ i n i t _ _ ( ) 
 
                 s e l f . s e t W i n d o w T i t l e ( " O b j e c t   R e p l a c e r   T o o l " ) 
 
                 s e l f . s e t G e o m e t r y ( 1 0 0 ,   1 0 0 ,   7 0 0 ,   7 0 0 ) 
 
                 
 
                 #   �0�0�0�0�0�0�0�0�0
 
                 m a i n _ w i d g e t   =   Q W i d g e t ( ) 
 
                 s e l f . s e t C e n t r a l W i d g e t ( m a i n _ w i d g e t ) 
 
                 
 
                 #   �0�0�0�0�0�0�0�0
 
                 m a i n _ l a y o u t   =   Q V B o x L a y o u t ( m a i n _ w i d g e t ) 
 
                 
 
                 #   C S V   f i l e   p a t h   i n p u t   a r e a 
 
                 c s v _ l a y o u t   =   Q H B o x L a y o u t ( ) 
 
                 c s v _ l a y o u t . a d d W i d g e t ( Q L a b e l ( " C S V   F i l e   P a t h : " ) ) 
 
 
 
                 s e l f . c s v _ p a t h _ e d i t   =   Q L i n e E d i t ( ) 
 
                 c s v _ l a y o u t . a d d W i d g e t ( s e l f . c s v _ p a t h _ e d i t ) 
 
 
 
                 b r o w s e _ b t n   =   Q P u s h B u t t o n ( " B r o w s e . . . " ) 
 
                 b r o w s e _ b t n . c l i c k e d . c o n n e c t ( s e l f . b r o w s e _ c s v ) 
 
                 c s v _ l a y o u t . a d d W i d g e t ( b r o w s e _ b t n ) 
 
 
 
                 m a i n _ l a y o u t . a d d L a y o u t ( c s v _ l a y o u t ) 
 
 
 
                 #   C S V   l o a d i n g   o p t i o n s 
 
                 c s v _ o p t i o n s _ l a y o u t   =   Q H B o x L a y o u t ( ) 
 
                 c s v _ o p t i o n s _ l a y o u t . a d d W i d g e t ( Q L a b e l ( " L o a d   O p t i o n s : " ) ) 
 
 
 
                 s e l f . s k i p _ h e a d e r _ c h e c k   =   Q C h e c k B o x ( " S k i p   H e a d e r   R o w " ) 
 
                 s e l f . s k i p _ h e a d e r _ c h e c k . s e t C h e c k e d ( T r u e ) 
 
                 s e l f . s k i p _ h e a d e r _ c h e c k . s e t T o o l T i p ( " C h e c k   i f   f i r s t   r o w   c o n t a i n s   h e a d e r s " ) 
 
                 c s v _ o p t i o n s _ l a y o u t . a d d W i d g e t ( s e l f . s k i p _ h e a d e r _ c h e c k ) 
 
 
 
                 c s v _ o p t i o n s _ l a y o u t . a d d W i d g e t ( Q L a b e l ( " S k i p   R o w s : " ) ) 
 
                 s e l f . s k i p _ r o w s _ s p i n b o x   =   Q S p i n B o x ( ) 
 
                 s e l f . s k i p _ r o w s _ s p i n b o x . s e t R a n g e ( 0 ,   1 0 ) 
 
                 s e l f . s k i p _ r o w s _ s p i n b o x . s e t V a l u e ( 0 ) 
 
                 s e l f . s k i p _ r o w s _ s p i n b o x . s e t T o o l T i p ( " N u m b e r   o f   r o w s   t o   s k i p   f r o m   t o p " ) 
 
                 c s v _ o p t i o n s _ l a y o u t . a d d W i d g e t ( s e l f . s k i p _ r o w s _ s p i n b o x ) 
 
                 
 
                 c s v _ o p t i o n s _ l a y o u t . a d d S t r e t c h ( ) 
 
                 m a i n _ l a y o u t . a d d L a y o u t ( c s v _ o p t i o n s _ l a y o u t ) 
 
                 
 
                 #   R e p l a c e m e n t   o p t i o n s 
 
                 o p t i o n s _ l a y o u t   =   Q H B o x L a y o u t ( ) 
 
                 o p t i o n s _ l a y o u t . a d d W i d g e t ( Q L a b e l ( " R e p l a c e m e n t   T y p e : " ) ) 
 
 
 
                 s e l f . b u t t o n _ g r o u p   =   Q B u t t o n G r o u p ( ) 
 
 
 
                 s e l f . i n s t a n c e _ r a d i o   =   Q R a d i o B u t t o n ( " I n s t a n c e " ) 
 
                 s e l f . i n s t a n c e _ r a d i o . s e t C h e c k e d ( T r u e ) 
 
                 s e l f . b u t t o n _ g r o u p . a d d B u t t o n ( s e l f . i n s t a n c e _ r a d i o ) 
 
                 o p t i o n s _ l a y o u t . a d d W i d g e t ( s e l f . i n s t a n c e _ r a d i o ) 
 
 
 
                 s e l f . r e f e r e n c e _ r a d i o   =   Q R a d i o B u t t o n ( " R e f e r e n c e " ) 
 
                 s e l f . b u t t o n _ g r o u p . a d d B u t t o n ( s e l f . r e f e r e n c e _ r a d i o ) 
 
                 o p t i o n s _ l a y o u t . a d d W i d g e t ( s e l f . r e f e r e n c e _ r a d i o ) 
 
                 
 
                 o p t i o n s _ l a y o u t . a d d S t r e t c h ( ) 
 
                 m a i n _ l a y o u t . a d d L a y o u t ( o p t i o n s _ l a y o u t ) 
 
                 
 
                 #   S t r i n g   m a n i p u l a t i o n   a r e a 
 
                 w i l d c a r d _ g r o u p   =   Q G r o u p B o x ( " S t r i n g   O p e r a t i o n s " ) 
 
                 w i l d c a r d _ l a y o u t   =   Q V B o x L a y o u t ( w i l d c a r d _ g r o u p ) 
 
 
 
                 #   P r e f i x / S u f f i x   s e t t i n g s 
 
                 p r e f i x _ s u f f i x _ l a y o u t   =   Q H B o x L a y o u t ( ) 
 
 
 
                 #   P r e f i x 
 
                 p r e f i x _ l a y o u t   =   Q V B o x L a y o u t ( ) 
 
                 p r e f i x _ l a y o u t . a d d W i d g e t ( Q L a b e l ( " P r e f i x : " ) ) 
 
                 s e l f . p r e f i x _ e d i t   =   Q L i n e E d i t ( ) 
 
                 s e l f . p r e f i x _ e d i t . s e t P l a c e h o l d e r T e x t ( " P r e f i x   t o   a d d " ) 
 
                 p r e f i x _ l a y o u t . a d d W i d g e t ( s e l f . p r e f i x _ e d i t ) 
 
 
 
                 #   S u f f i x 
 
                 s u f f i x _ l a y o u t   =   Q V B o x L a y o u t ( ) 
 
                 s u f f i x _ l a y o u t . a d d W i d g e t ( Q L a b e l ( " S u f f i x : " ) ) 
 
                 s e l f . s u f f i x _ e d i t   =   Q L i n e E d i t ( ) 
 
                 s e l f . s u f f i x _ e d i t . s e t P l a c e h o l d e r T e x t ( " S u f f i x   t o   a d d " ) 
 
                 s u f f i x _ l a y o u t . a d d W i d g e t ( s e l f . s u f f i x _ e d i t ) 
 
                 
 
                 p r e f i x _ s u f f i x _ l a y o u t . a d d L a y o u t ( p r e f i x _ l a y o u t ) 
 
                 p r e f i x _ s u f f i x _ l a y o u t . a d d L a y o u t ( s u f f i x _ l a y o u t ) 
 
                 
 
                 w i l d c a r d _ l a y o u t . a d d L a y o u t ( p r e f i x _ s u f f i x _ l a y o u t ) 
 
                 
 
                 #   R e m o v e   w o r d   s e t t i n g s 
 
                 r e m o v e _ w o r d _ l a y o u t   =   Q H B o x L a y o u t ( ) 
 
                 r e m o v e _ w o r d _ l a y o u t . a d d W i d g e t ( Q L a b e l ( " R e m o v e   W o r d : " ) ) 
 
                 s e l f . r e m o v e _ w o r d _ e d i t   =   Q L i n e E d i t ( ) 
 
                 s e l f . r e m o v e _ w o r d _ e d i t . s e t P l a c e h o l d e r T e x t ( " S t r i n g   t o   r e m o v e " ) 
 
                 r e m o v e _ w o r d _ l a y o u t . a d d W i d g e t ( s e l f . r e m o v e _ w o r d _ e d i t ) 
 
                 w i l d c a r d _ l a y o u t . a d d L a y o u t ( r e m o v e _ w o r d _ l a y o u t ) 
 
 
 
                 #   A p p l y   t a r g e t   s e l e c t i o n 
 
                 t a r g e t _ l a y o u t   =   Q H B o x L a y o u t ( ) 
 
                 t a r g e t _ l a y o u t . a d d W i d g e t ( Q L a b e l ( " A p p l y   T o : " ) ) 
 
 
 
                 s e l f . a p p l y _ t o _ s o u r c e _ c h e c k   =   Q C h e c k B o x ( " S o u r c e " ) 
 
                 s e l f . a p p l y _ t o _ s o u r c e _ c h e c k . s e t C h e c k e d ( T r u e ) 
 
                 t a r g e t _ l a y o u t . a d d W i d g e t ( s e l f . a p p l y _ t o _ s o u r c e _ c h e c k ) 
 
 
 
                 s e l f . a p p l y _ t o _ t a r g e t _ c h e c k   =   Q C h e c k B o x ( " T a r g e t " ) 
 
                 s e l f . a p p l y _ t o _ t a r g e t _ c h e c k . s e t C h e c k e d ( F a l s e ) 
 
                 t a r g e t _ l a y o u t . a d d W i d g e t ( s e l f . a p p l y _ t o _ t a r g e t _ c h e c k ) 
 
                 
 
                 t a r g e t _ l a y o u t . a d d S t r e t c h ( ) 
 
                 w i l d c a r d _ l a y o u t . a d d L a y o u t ( t a r g e t _ l a y o u t ) 
 
                 
 
                 #   A p p l y   b u t t o n s 
 
                 b u t t o n _ l a y o u t   =   Q H B o x L a y o u t ( ) 
 
                 a p p l y _ p r e f i x _ s u f f i x _ b t n   =   Q P u s h B u t t o n ( " A p p l y   P r e f i x / S u f f i x " ) 
 
                 a p p l y _ p r e f i x _ s u f f i x _ b t n . c l i c k e d . c o n n e c t ( s e l f . a p p l y _ p r e f i x _ s u f f i x ) 
 
                 b u t t o n _ l a y o u t . a d d W i d g e t ( a p p l y _ p r e f i x _ s u f f i x _ b t n ) 
 
 
 
                 r e m o v e _ w o r d _ b t n   =   Q P u s h B u t t o n ( " R e m o v e   W o r d " ) 
 
                 r e m o v e _ w o r d _ b t n . c l i c k e d . c o n n e c t ( s e l f . r e m o v e _ w o r d ) 
 
                 b u t t o n _ l a y o u t . a d d W i d g e t ( r e m o v e _ w o r d _ b t n ) 
 
                 
 
                 w i l d c a r d _ l a y o u t . a d d L a y o u t ( b u t t o n _ l a y o u t ) 
 
                 
 
                 m a i n _ l a y o u t . a d d W i d g e t ( w i l d c a r d _ g r o u p ) 
 
                 
 
                 #   T a b l e   w i d g e t 
 
                 s e l f . t a b l e _ w i d g e t   =   Q T a b l e W i d g e t ( ) 
 
                 s e l f . t a b l e _ w i d g e t . s e t C o l u m n C o u n t ( 3 ) 
 
                 s e l f . t a b l e _ w i d g e t . s e t H o r i z o n t a l H e a d e r L a b e l s ( [ " S o u r c e   O b j e c t " ,   " T a r g e t   O b j e c t " ,   " S t a t u s " ] ) 
 
                 
 
                 #   �0�0�0�0�0�0�0n0�0�0�0�0�0-��[�f�0D0r�k0	Y�f	�
 
                 s c r o l l b a r _ s t y l e   =   " " " 
 
                         Q S c r o l l B a r : v e r t i c a l   { 
 
                                 b a c k g r o u n d - c o l o r :   # 4 7 4 7 4 7 ; 
 
                                 b o r d e r :   1 p x   s o l i d   # d 0 d 0 d 0 ; 
 
                                 b o r d e r - r a d i u s :   6 p x ; 
 
                                 w i d t h :   1 6 p x ; 
 
                                 m a r g i n :   0 p x ; 
 
                         } 
 
                         
 
                         Q S c r o l l B a r : : h a n d l e : v e r t i c a l   { 
 
                                 b a c k g r o u n d - c o l o r :   # e 3 e 3 e 3 ; 
 
                                 b o r d e r - r a d i u s :   6 p x ; 
 
                                 m i n - h e i g h t :   2 0 p x ; 
 
                                 m a r g i n :   2 p x ; 
 
                         } 
 
                         
 
                         Q S c r o l l B a r : : h a n d l e : v e r t i c a l : h o v e r   { 
 
                                 b a c k g r o u n d - c o l o r :   # 1 0 6 e b e ; 
 
                         } 
 
                         
 
                         Q S c r o l l B a r : : h a n d l e : v e r t i c a l : p r e s s e d   { 
 
                                 b a c k g r o u n d - c o l o r :   # 0 0 5 a 9 e ; 
 
                         } 
 
                         
 
                         Q S c r o l l B a r : : a d d - l i n e : v e r t i c a l , 
 
                         Q S c r o l l B a r : : s u b - l i n e : v e r t i c a l   { 
 
                                 b a c k g r o u n d :   n o n e ; 
 
                                 b o r d e r :   n o n e ; 
 
                                 h e i g h t :   0 p x ; 
 
                         } 
 
                         
 
                         Q S c r o l l B a r : : a d d - p a g e : v e r t i c a l , 
 
                         Q S c r o l l B a r : : s u b - p a g e : v e r t i c a l   { 
 
                                 b a c k g r o u n d :   n o n e ; 
 
                         } 
 
                         
 
                         Q S c r o l l B a r : h o r i z o n t a l   { 
 
                                 b a c k g r o u n d - c o l o r :   # f 0 f 0 f 0 ; 
 
                                 b o r d e r :   1 p x   s o l i d   # d 0 d 0 d 0 ; 
 
                                 b o r d e r - r a d i u s :   6 p x ; 
 
                                 h e i g h t :   1 6 p x ; 
 
                                 m a r g i n :   0 p x ; 
 
                         } 
 
                         
 
                         Q S c r o l l B a r : : h a n d l e : h o r i z o n t a l   { 
 
                                 b a c k g r o u n d - c o l o r :   # 0 0 7 8 d 4 ; 
 
                                 b o r d e r - r a d i u s :   6 p x ; 
 
                                 m i n - w i d t h :   2 0 p x ; 
 
                                 m a r g i n :   2 p x ; 
 
                         } 
 
                         
 
                         Q S c r o l l B a r : : h a n d l e : h o r i z o n t a l : h o v e r   { 
 
                                 b a c k g r o u n d - c o l o r :   # 1 0 6 e b e ; 
 
                         } 
 
                         
 
                         Q S c r o l l B a r : : h a n d l e : h o r i z o n t a l : p r e s s e d   { 
 
                                 b a c k g r o u n d - c o l o r :   # 0 0 5 a 9 e ; 
 
                         } 
 
                         
 
                         Q S c r o l l B a r : : a d d - l i n e : h o r i z o n t a l , 
 
                         Q S c r o l l B a r : : s u b - l i n e : h o r i z o n t a l   { 
 
                                 b a c k g r o u n d :   n o n e ; 
 
                                 b o r d e r :   n o n e ; 
 
                                 w i d t h :   0 p x ; 
 
                         } 
 
                         
 
                         Q S c r o l l B a r : : a d d - p a g e : h o r i z o n t a l , 
 
                         Q S c r o l l B a r : : s u b - p a g e : h o r i z o n t a l   { 
 
                                 b a c k g r o u n d :   n o n e ; 
 
                         } 
 
                 " " " 
 
                 
 
                 s e l f . t a b l e _ w i d g e t . s e t S t y l e S h e e t ( s c r o l l b a r _ s t y l e ) 
 
                 
 
                 #   �0�0�0�0n0�}Ɩ-��[��rKaR�NYo0�}Ɩ�S��	�
 
                 s e l f . t a b l e _ w i d g e t . i t e m C h a n g e d . c o n n e c t ( s e l f . o n _ i t e m _ c h a n g e d ) 
 
                 
 
                 #   �pex��b�0	g�Rk0Y0�0
 
                 s e l f . t a b l e _ w i d g e t . s e t S e l e c t i o n B e h a v i o r ( Q T a b l e W i d g e t . S e l e c t R o w s ) 
 
                 s e l f . t a b l e _ w i d g e t . s e t S e l e c t i o n M o d e ( Q T a b l e W i d g e t . M u l t i S e l e c t i o n ) 
 
                 
 
                 #   �0�0�0E^�0��te
 
                 h e a d e r   =   s e l f . t a b l e _ w i d g e t . h o r i z o n t a l H e a d e r ( ) 
 
                 h e a d e r . s e t S e c t i o n R e s i z e M o d e ( 0 ,   Q H e a d e r V i e w . S t r e t c h ) 
 
                 h e a d e r . s e t S e c t i o n R e s i z e M o d e ( 1 ,   Q H e a d e r V i e w . S t r e t c h ) 
 
                 h e a d e r . s e t S e c t i o n R e s i z e M o d e ( 2 ,   Q H e a d e r V i e w . R e s i z e T o C o n t e n t s ) 
 
                 
 
                 m a i n _ l a y o u t . a d d W i d g e t ( s e l f . t a b l e _ w i d g e t ) 
 
                 
 
                 #   H e l p   i n f o r m a t i o n 
 
                 h e l p _ l a y o u t   =   Q H B o x L a y o u t ( ) 
 
                 h e l p _ l a b e l   =   Q L a b e l ( " H e l p :   W i l d c a r d s   ( * )   c a n   b e   u s e d   i n   o b j e c t   n a m e s .   E x a m p l e :   ' B o x * '   m a t c h e s   ' B o x 0 0 1 ' ,   ' B o x _ r e d ' ,   e t c . " ) 
 
                 h e l p _ l a b e l . s e t S t y l e S h e e t ( " c o l o r :   w h i t e ;   f o n t - s i z e :   1 0 p x ; " ) 
 
                 h e l p _ l a y o u t . a d d W i d g e t ( h e l p _ l a b e l ) 
 
                 h e l p _ l a y o u t . a d d S t r e t c h ( ) 
 
                 m a i n _ l a y o u t . a d d L a y o u t ( h e l p _ l a y o u t ) 
 
 
 
                 #   B u t t o n   a r e a 
 
                 b u t t o n s _ l a y o u t   =   Q H B o x L a y o u t ( ) 
 
 
 
                 l o a d _ b t n   =   Q P u s h B u t t o n ( " L o a d   C S V " ) 
 
                 l o a d _ b t n . c l i c k e d . c o n n e c t ( s e l f . l o a d _ c s v ) 
 
                 b u t t o n s _ l a y o u t . a d d W i d g e t ( l o a d _ b t n ) 
 
 
 
                 s a v e _ b t n   =   Q P u s h B u t t o n ( " S a v e   C S V " ) 
 
                 s a v e _ b t n . c l i c k e d . c o n n e c t ( s e l f . s a v e _ c s v ) 
 
                 b u t t o n s _ l a y o u t . a d d W i d g e t ( s a v e _ b t n ) 
 
 
 
                 a d d _ r o w _ b t n   =   Q P u s h B u t t o n ( " A d d   R o w " ) 
 
                 a d d _ r o w _ b t n . c l i c k e d . c o n n e c t ( s e l f . a d d _ r o w ) 
 
                 b u t t o n s _ l a y o u t . a d d W i d g e t ( a d d _ r o w _ b t n ) 
 
 
 
                 d e l e t e _ r o w _ b t n   =   Q P u s h B u t t o n ( " D e l e t e   R o w " ) 
 
                 d e l e t e _ r o w _ b t n . c l i c k e d . c o n n e c t ( s e l f . d e l e t e _ r o w ) 
 
                 b u t t o n s _ l a y o u t . a d d W i d g e t ( d e l e t e _ r o w _ b t n ) 
 
 
 
                 r e p l a c e _ b t n   =   Q P u s h B u t t o n ( " E x e c u t e   R e p l a c e " ) 
 
                 r e p l a c e _ b t n . c l i c k e d . c o n n e c t ( s e l f . r e p l a c e _ o b j e c t s ) 
 
                 b u t t o n s _ l a y o u t . a d d W i d g e t ( r e p l a c e _ b t n ) 
 
 
 
                 c l o s e _ b t n   =   Q P u s h B u t t o n ( " C l o s e " ) 
 
                 c l o s e _ b t n . c l i c k e d . c o n n e c t ( s e l f . c l o s e _ w i n d o w ) 
 
                 b u t t o n s _ l a y o u t . a d d W i d g e t ( c l o s e _ b t n ) 
 
                 
 
                 m a i n _ l a y o u t . a d d L a y o u t ( b u t t o n s _ l a y o u t ) 
 
                 
 
         d e f   b r o w s e _ c s v ( s e l f ) : 
 
                 " " " D i s p l a y   d i a l o g   t o   s e l e c t   C S V   f i l e " " " 
 
                 f i l e _ p a t h ,   _   =   Q F i l e D i a l o g . g e t O p e n F i l e N a m e ( 
 
                         s e l f , 
 
                         " S e l e c t   C S V   F i l e " , 
 
                         " " , 
 
                         " C S V   F i l e s   ( * . c s v ) ; ; A l l   F i l e s   ( * ) " 
 
                 ) 
 
                 
 
                 i f   f i l e _ p a t h : 
 
                         s e l f . c s v _ p a t h _ e d i t . s e t T e x t ( f i l e _ p a t h ) 
 
                         
 
         d e f   l o a d _ c s v ( s e l f ) : 
 
                 " " " L o a d   C S V   f i l e   a n d   d i s p l a y   i n   t a b l e " " " 
 
                 c s v _ p a t h   =   s e l f . c s v _ p a t h _ e d i t . t e x t ( ) . s t r i p ( ) 
 
 
 
                 i f   n o t   c s v _ p a t h : 
 
                         Q M e s s a g e B o x . w a r n i n g ( s e l f ,   " E r r o r " ,   " P l e a s e   s p e c i f y   a   C S V   f i l e " ) 
 
                         r e t u r n 
 
 
 
                 i f   n o t   o s . p a t h . e x i s t s ( c s v _ p a t h ) : 
 
                         Q M e s s a g e B o x . w a r n i n g ( s e l f ,   " E r r o r " ,   " T h e   s p e c i f i e d   C S V   f i l e   d o e s   n o t   e x i s t " ) 
 
                         r e t u r n 
 
                         
 
                 #   �0�0�0�0�0�0�0�0
 
                 s e l f . t a b l e _ w i d g e t . s e t R o w C o u n t ( 0 ) 
 
                 
 
                 #   ��0��0�0�0�0�0�0�0�S�_
 
                 s k i p _ h e a d e r   =   s e l f . s k i p _ h e a d e r _ c h e c k . i s C h e c k e d ( ) 
 
                 s k i p _ r o w s   =   s e l f . s k i p _ r o w s _ s p i n b o x . v a l u e ( ) 
 
                 
 
                 #   �pen0�0�0�0�0�0�0�0�0�0f�Y0
 
                 e n c o d i n g s   =   [ ' u t f - 8 ' ,   ' s h i f t - j i s ' ,   ' c p 9 3 2 ' ,   ' u t f - 8 - s i g ' ] 
 
                 
 
                 f o r   e n c o d i n g   i n   e n c o d i n g s : 
 
                         t r y : 
 
                                 w i t h   o p e n ( c s v _ p a t h ,   ' r ' ,   e n c o d i n g = e n c o d i n g )   a s   f : 
 
                                         r e a d e r   =   c s v . r e a d e r ( f ) 
 
                                         
 
                                         #   hQf0n0L��0��0���0
 
                                         a l l _ r o w s   =   l i s t ( r e a d e r ) 
 
                                         
 
                                         #   �0�0�0�0Y0�0L�pe�0��{
 
                                         t o t a l _ s k i p   =   s k i p _ r o w s 
 
                                         i f   s k i p _ h e a d e r : 
 
                                                 t o t a l _ s k i p   + =   1 
 
                                         
 
                                         #   �0�0�0�0W0_0L��0d�Y
 
                                         d a t a _ r o w s   =   a l l _ r o w s [ t o t a l _ s k i p : ] 
 
                                         
 
                                         #   �0�0�0�0k0���R
 
                                         t a b l e _ r o w _ i n d e x   =   0 
 
                                         f o r   r o w   i n   d a t a _ r o w s : 
 
                                                 i f   l e n ( r o w )   > =   2 : 
 
                                                         #   �eW0D0L��0���R
 
                                                         s e l f . t a b l e _ w i d g e t . i n s e r t R o w ( t a b l e _ r o w _ i n d e x ) 
 
                                                         
 
                                                         #   S e t   d a t a 
 
                                                         s o u r c e _ i t e m   =   Q T a b l e W i d g e t I t e m ( r o w [ 0 ] . s t r i p ( ) ) 
 
                                                         t a r g e t _ i t e m   =   Q T a b l e W i d g e t I t e m ( r o w [ 1 ] . s t r i p ( ) ) 
 
                                                         s t a t u s _ i t e m   =   Q T a b l e W i d g e t I t e m ( " P e n d i n g " ) 
 
 
 
                                                         #   M a k e   s t a t u s   c o l u m n   n o n - e d i t a b l e 
 
                                                         s t a t u s _ i t e m . s e t F l a g s ( s t a t u s _ i t e m . f l a g s ( )   &   ~ Q t . I t e m I s E d i t a b l e ) 
 
                                                         
 
                                                         s e l f . t a b l e _ w i d g e t . s e t I t e m ( t a b l e _ r o w _ i n d e x ,   0 ,   s o u r c e _ i t e m ) 
 
                                                         s e l f . t a b l e _ w i d g e t . s e t I t e m ( t a b l e _ r o w _ i n d e x ,   1 ,   t a r g e t _ i t e m ) 
 
                                                         s e l f . t a b l e _ w i d g e t . s e t I t e m ( t a b l e _ r o w _ i n d e x ,   2 ,   s t a t u s _ i t e m ) 
 
                                                         
 
                                                         t a b l e _ r o w _ i n d e x   + =   1 
 
                                 
 
                                 #   L o a d   c o m p l e t i o n   m e s s a g e 
 
                                 s k i p p e d _ i n f o   =   [ ] 
 
                                 i f   s k i p _ h e a d e r : 
 
                                         s k i p p e d _ i n f o . a p p e n d ( " h e a d e r   r o w " ) 
 
                                 i f   s k i p _ r o w s   >   0 : 
 
                                         s k i p p e d _ i n f o . a p p e n d ( f " { s k i p _ r o w s }   r o w s " ) 
 
 
 
                                 s k i p _ m e s s a g e   =   " " 
 
                                 i f   s k i p p e d _ i n f o : 
 
                                         s k i p _ m e s s a g e   =   f " \ n S k i p p e d :   { ' ,   ' . j o i n ( s k i p p e d _ i n f o ) } " 
 
 
 
                                 Q M e s s a g e B o x . i n f o r m a t i o n ( 
 
                                         s e l f , 
 
                                         " C o m p l e t e " , 
 
                                         f " L o a d e d   { s e l f . t a b l e _ w i d g e t . r o w C o u n t ( ) }   d a t a   i t e m s \ n " 
 
                                         f " E n c o d i n g :   { e n c o d i n g } { s k i p _ m e s s a g e } " 
 
                                 ) 
 
                                 r e t u r n     #   E x i t   o n   s u c c e s s 
 
 
 
                         e x c e p t   U n i c o d e D e c o d e E r r o r : 
 
                                 #   I f   t h i s   e n c o d i n g   d o e s n ' t   w o r k ,   t r y   t h e   n e x t   o n e 
 
                                 c o n t i n u e 
 
                         e x c e p t   E x c e p t i o n   a s   e : 
 
                                 #   L o g   o t h e r   e r r o r s   a n d   t r y   t h e   n e x t   e n c o d i n g 
 
                                 p r i n t ( f " E r r o r   w i t h   e n c o d i n g   { e n c o d i n g } :   { s t r ( e ) } " ) 
 
                                 c o n t i n u e 
 
 
 
                 #   I f   a l l   e n c o d i n g s   f a i l e d 
 
                 Q M e s s a g e B o x . c r i t i c a l ( s e l f ,   " E r r o r " ,   " F a i l e d   t o   l o a d   C S V \ n S u p p o r t e d   e n c o d i n g s :   U T F - 8 ,   S h i f t - J I S ,   C P 9 3 2 " ) 
 
         
 
         d e f   s a v e _ c s v ( s e l f ) : 
 
                 " " " S a v e   t a b l e   c o n t e n t s   t o   C S V   f i l e " " " 
 
                 i f   s e l f . t a b l e _ w i d g e t . r o w C o u n t ( )   = =   0 : 
 
                         Q M e s s a g e B o x . w a r n i n g ( s e l f ,   " E r r o r " ,   " N o   d a t a   t o   s a v e " ) 
 
                         r e t u r n 
 
 
 
                 f i l e _ p a t h ,   _   =   Q F i l e D i a l o g . g e t S a v e F i l e N a m e ( 
 
                         s e l f , 
 
                         " S a v e   C S V   F i l e " , 
 
                         " " , 
 
                         " C S V   F i l e s   ( * . c s v ) ; ; A l l   F i l e s   ( * ) " 
 
                 ) 
 
 
 
                 i f   n o t   f i l e _ p a t h : 
 
                         r e t u r n 
 
 
 
                 t r y : 
 
                         w i t h   o p e n ( f i l e _ p a t h ,   ' w ' ,   n e w l i n e = ' ' ,   e n c o d i n g = ' u t f - 8 - s i g ' )   a s   f : 
 
                                 w r i t e r   =   c s v . w r i t e r ( f ) 
 
 
 
                                 #   W r i t e   h e a d e r   ( o p t i o n a l ) 
 
                                 #   w r i t e r . w r i t e r o w ( [ " S o u r c e " ,   " T a r g e t " ] ) 
 
 
 
                                 #   W r i t e   d a t a 
 
                                 f o r   r o w   i n   r a n g e ( s e l f . t a b l e _ w i d g e t . r o w C o u n t ( ) ) : 
 
                                         s o u r c e _ i t e m   =   s e l f . t a b l e _ w i d g e t . i t e m ( r o w ,   0 ) 
 
                                         t a r g e t _ i t e m   =   s e l f . t a b l e _ w i d g e t . i t e m ( r o w ,   1 ) 
 
 
 
                                         i f   s o u r c e _ i t e m   a n d   t a r g e t _ i t e m : 
 
                                                 w r i t e r . w r i t e r o w ( [ s o u r c e _ i t e m . t e x t ( ) ,   t a r g e t _ i t e m . t e x t ( ) ] ) 
 
 
 
                         Q M e s s a g e B o x . i n f o r m a t i o n ( s e l f ,   " C o m p l e t e " ,   f " C S V   f i l e   s a v e d \ n { f i l e _ p a t h } " ) 
 
 
 
                 e x c e p t   E x c e p t i o n   a s   e : 
 
                         Q M e s s a g e B o x . c r i t i c a l ( s e l f ,   " E r r o r " ,   f " F a i l e d   t o   s a v e   C S V   f i l e \ n { s t r ( e ) } " ) 
 
         
 
         d e f   r e m o v e _ w o r d ( s e l f ) : 
 
                 " " " R e m o v e   s p e c i f i e d   w o r d   ( s e l e c t e d   r o w s   o n l y ) " " " 
 
                 r e m o v e _ w o r d   =   s e l f . r e m o v e _ w o r d _ e d i t . t e x t ( ) . s t r i p ( ) 
 
 
 
                 i f   n o t   r e m o v e _ w o r d : 
 
                         Q M e s s a g e B o x . w a r n i n g ( s e l f ,   " E r r o r " ,   " P l e a s e   e n t e r   a   w o r d   t o   r e m o v e " ) 
 
                         r e t u r n 
 
 
 
                 #   C h e c k   a p p l y   t a r g e t s 
 
                 a p p l y _ t o _ s o u r c e   =   s e l f . a p p l y _ t o _ s o u r c e _ c h e c k . i s C h e c k e d ( ) 
 
                 a p p l y _ t o _ t a r g e t   =   s e l f . a p p l y _ t o _ t a r g e t _ c h e c k . i s C h e c k e d ( ) 
 
 
 
                 i f   n o t   a p p l y _ t o _ s o u r c e   a n d   n o t   a p p l y _ t o _ t a r g e t : 
 
                         Q M e s s a g e B o x . w a r n i n g ( s e l f ,   " E r r o r " ,   " P l e a s e   s e l e c t   a p p l y   t a r g e t s " ) 
 
                         r e t u r n 
 
 
 
                 #   G e t   s e l e c t e d   r o w s 
 
                 s e l e c t e d _ r o w s   =   s e t ( ) 
 
                 f o r   i t e m   i n   s e l f . t a b l e _ w i d g e t . s e l e c t e d I t e m s ( ) : 
 
                         s e l e c t e d _ r o w s . a d d ( i t e m . r o w ( ) ) 
 
 
 
                 i f   n o t   s e l e c t e d _ r o w s : 
 
                         Q M e s s a g e B o x . w a r n i n g ( s e l f ,   " E r r o r " ,   " P l e a s e   s e l e c t   r o w s   t o   r e m o v e   w o r d   f r o m " ) 
 
                         r e t u r n 
 
                         
 
                 u p d a t e d _ c o u n t   =   0 
 
 
 
                 f o r   r o w   i n   s e l e c t e d _ r o w s : 
 
                         #   R e m o v e   f r o m   s o u r c e 
 
                         i f   a p p l y _ t o _ s o u r c e : 
 
                                 s o u r c e _ i t e m   =   s e l f . t a b l e _ w i d g e t . i t e m ( r o w ,   0 ) 
 
                                 i f   s o u r c e _ i t e m : 
 
                                         s o u r c e _ t e x t   =   s o u r c e _ i t e m . t e x t ( ) 
 
                                         n e w _ t e x t   =   s o u r c e _ t e x t . r e p l a c e ( r e m o v e _ w o r d ,   ' ' ) 
 
 
 
                                         i f   n e w _ t e x t   ! =   s o u r c e _ t e x t : 
 
                                                 s o u r c e _ i t e m . s e t T e x t ( n e w _ t e x t ) 
 
                                                 u p d a t e d _ c o u n t   + =   1 
 
 
 
                         #   R e m o v e   f r o m   t a r g e t 
 
                         i f   a p p l y _ t o _ t a r g e t : 
 
                                 t a r g e t _ i t e m   =   s e l f . t a b l e _ w i d g e t . i t e m ( r o w ,   1 ) 
 
                                 i f   t a r g e t _ i t e m : 
 
                                         t a r g e t _ t e x t   =   t a r g e t _ i t e m . t e x t ( ) 
 
                                         n e w _ t e x t   =   t a r g e t _ t e x t . r e p l a c e ( r e m o v e _ w o r d ,   ' ' ) 
 
 
 
                                         i f   n e w _ t e x t   ! =   t a r g e t _ t e x t : 
 
                                                 t a r g e t _ i t e m . s e t T e x t ( n e w _ t e x t ) 
 
                                                 u p d a t e d _ c o u n t   + =   1 
 
 
 
                 Q M e s s a g e B o x . i n f o r m a t i o n ( s e l f ,   " C o m p l e t e " ,   f " R e m o v e d   w o r d   f r o m   { u p d a t e d _ c o u n t }   i t e m s " ) 
 
         
 
         d e f   a p p l y _ p r e f i x _ s u f f i x ( s e l f ) : 
 
                 " " " A p p l y   p r e f i x / s u f f i x   ( s e l e c t e d   r o w s   o n l y ) " " " 
 
                 p r e f i x   =   s e l f . p r e f i x _ e d i t . t e x t ( ) . s t r i p ( ) 
 
                 s u f f i x   =   s e l f . s u f f i x _ e d i t . t e x t ( ) . s t r i p ( ) 
 
 
 
                 i f   n o t   p r e f i x   a n d   n o t   s u f f i x : 
 
                         Q M e s s a g e B o x . w a r n i n g ( s e l f ,   " E r r o r " ,   " P l e a s e   e n t e r   p r e f i x   o r   s u f f i x " ) 
 
                         r e t u r n 
 
 
 
                 #   C h e c k   a p p l y   t a r g e t s 
 
                 a p p l y _ t o _ s o u r c e   =   s e l f . a p p l y _ t o _ s o u r c e _ c h e c k . i s C h e c k e d ( ) 
 
                 a p p l y _ t o _ t a r g e t   =   s e l f . a p p l y _ t o _ t a r g e t _ c h e c k . i s C h e c k e d ( ) 
 
 
 
                 i f   n o t   a p p l y _ t o _ s o u r c e   a n d   n o t   a p p l y _ t o _ t a r g e t : 
 
                         Q M e s s a g e B o x . w a r n i n g ( s e l f ,   " E r r o r " ,   " P l e a s e   s e l e c t   a p p l y   t a r g e t s " ) 
 
                         r e t u r n 
 
 
 
                 #   G e t   s e l e c t e d   r o w s 
 
                 s e l e c t e d _ r o w s   =   s e t ( ) 
 
                 f o r   i t e m   i n   s e l f . t a b l e _ w i d g e t . s e l e c t e d I t e m s ( ) : 
 
                         s e l e c t e d _ r o w s . a d d ( i t e m . r o w ( ) ) 
 
 
 
                 i f   n o t   s e l e c t e d _ r o w s : 
 
                         Q M e s s a g e B o x . w a r n i n g ( s e l f ,   " E r r o r " ,   " P l e a s e   s e l e c t   r o w s   t o   a p p l y   p r e f i x / s u f f i x   t o " ) 
 
                         r e t u r n 
 
                         
 
                 u p d a t e d _ c o u n t   =   0 
 
 
 
                 f o r   r o w   i n   s e l e c t e d _ r o w s : 
 
                         #   A p p l y   t o   s o u r c e 
 
                         i f   a p p l y _ t o _ s o u r c e : 
 
                                 s o u r c e _ i t e m   =   s e l f . t a b l e _ w i d g e t . i t e m ( r o w ,   0 ) 
 
                                 i f   s o u r c e _ i t e m : 
 
                                         s o u r c e _ t e x t   =   s o u r c e _ i t e m . t e x t ( ) 
 
                                         n e w _ t e x t   =   s o u r c e _ t e x t 
 
 
 
                                         #   A d d   p r e f i x 
 
                                         i f   p r e f i x : 
 
                                                 n e w _ t e x t   =   p r e f i x   +   n e w _ t e x t 
 
 
 
                                         #   A d d   s u f f i x 
 
                                         i f   s u f f i x : 
 
                                                 n e w _ t e x t   =   n e w _ t e x t   +   s u f f i x 
 
 
 
                                         i f   n e w _ t e x t   ! =   s o u r c e _ t e x t : 
 
                                                 s o u r c e _ i t e m . s e t T e x t ( n e w _ t e x t ) 
 
                                                 u p d a t e d _ c o u n t   + =   1 
 
 
 
                         #   A p p l y   t o   t a r g e t 
 
                         i f   a p p l y _ t o _ t a r g e t : 
 
                                 t a r g e t _ i t e m   =   s e l f . t a b l e _ w i d g e t . i t e m ( r o w ,   1 ) 
 
                                 i f   t a r g e t _ i t e m : 
 
                                         t a r g e t _ t e x t   =   t a r g e t _ i t e m . t e x t ( ) 
 
                                         n e w _ t e x t   =   t a r g e t _ t e x t 
 
 
 
                                         #   A d d   p r e f i x 
 
                                         i f   p r e f i x : 
 
                                                 n e w _ t e x t   =   p r e f i x   +   n e w _ t e x t 
 
 
 
                                         #   A d d   s u f f i x 
 
                                         i f   s u f f i x : 
 
                                                 n e w _ t e x t   =   n e w _ t e x t   +   s u f f i x 
 
 
 
                                         i f   n e w _ t e x t   ! =   t a r g e t _ t e x t : 
 
                                                 t a r g e t _ i t e m . s e t T e x t ( n e w _ t e x t ) 
 
                                                 u p d a t e d _ c o u n t   + =   1 
 
 
 
                 Q M e s s a g e B o x . i n f o r m a t i o n ( s e l f ,   " C o m p l e t e " ,   f " U p d a t e d   { u p d a t e d _ c o u n t }   i t e m s " ) 
 
                         
 
         d e f   o n _ i t e m _ c h a n g e d ( s e l f ,   i t e m ) : 
 
                 " " " P r o c e s s   w h e n   t a b l e   i t e m   i s   c h a n g e d " " " 
 
                 #   R e s e t   s t a t u s   o f   e d i t e d   r o w   t o   " P e n d i n g " 
 
                 r o w   =   i t e m . r o w ( ) 
 
                 i f   i t e m . c o l u m n ( )   <   2 :     #   I f   s o u r c e   o r   t a r g e t   w a s   c h a n g e d 
 
                         s t a t u s _ i t e m   =   Q T a b l e W i d g e t I t e m ( " P e n d i n g " ) 
 
                         s t a t u s _ i t e m . s e t F l a g s ( s t a t u s _ i t e m . f l a g s ( )   &   ~ Q t . I t e m I s E d i t a b l e ) 
 
                         s e l f . t a b l e _ w i d g e t . s e t I t e m ( r o w ,   2 ,   s t a t u s _ i t e m ) 
 
         
 
         d e f   a d d _ r o w ( s e l f ) : 
 
                 " " " A d d   n e w   r o w " " " 
 
                 r o w _ c o u n t   =   s e l f . t a b l e _ w i d g e t . r o w C o u n t ( ) 
 
                 s e l f . t a b l e _ w i d g e t . i n s e r t R o w ( r o w _ c o u n t ) 
 
 
 
                 #   C r e a t e   e m p t y   i t e m s 
 
                 s o u r c e _ i t e m   =   Q T a b l e W i d g e t I t e m ( " " ) 
 
                 t a r g e t _ i t e m   =   Q T a b l e W i d g e t I t e m ( " " ) 
 
                 s t a t u s _ i t e m   =   Q T a b l e W i d g e t I t e m ( " P e n d i n g " ) 
 
                 s t a t u s _ i t e m . s e t F l a g s ( s t a t u s _ i t e m . f l a g s ( )   &   ~ Q t . I t e m I s E d i t a b l e ) 
 
 
 
                 s e l f . t a b l e _ w i d g e t . s e t I t e m ( r o w _ c o u n t ,   0 ,   s o u r c e _ i t e m ) 
 
                 s e l f . t a b l e _ w i d g e t . s e t I t e m ( r o w _ c o u n t ,   1 ,   t a r g e t _ i t e m ) 
 
                 s e l f . t a b l e _ w i d g e t . s e t I t e m ( r o w _ c o u n t ,   2 ,   s t a t u s _ i t e m ) 
 
 
 
                 #   S e t   f o c u s   t o   n e w   r o w 
 
                 s e l f . t a b l e _ w i d g e t . s e t C u r r e n t C e l l ( r o w _ c o u n t ,   0 ) 
 
 
 
         d e f   d e l e t e _ r o w ( s e l f ) : 
 
                 " " " D e l e t e   s e l e c t e d   r o w " " " 
 
                 c u r r e n t _ r o w   =   s e l f . t a b l e _ w i d g e t . c u r r e n t R o w ( ) 
 
                 i f   c u r r e n t _ r o w   > =   0 : 
 
                         r e p l y   =   Q M e s s a g e B o x . q u e s t i o n ( 
 
                                 s e l f , 
 
                                 " C o n f i r m " , 
 
                                 " D e l e t e   s e l e c t e d   r o w ? " , 
 
                                 Q M e s s a g e B o x . Y e s   |   Q M e s s a g e B o x . N o , 
 
                                 Q M e s s a g e B o x . N o 
 
                         ) 
 
                         i f   r e p l y   = =   Q M e s s a g e B o x . Y e s : 
 
                                 s e l f . t a b l e _ w i d g e t . r e m o v e R o w ( c u r r e n t _ r o w ) 
 
                 e l s e : 
 
                         Q M e s s a g e B o x . i n f o r m a t i o n ( s e l f ,   " I n f o " ,   " P l e a s e   s e l e c t   a   r o w   t o   d e l e t e " ) 
 
         
 
         d e f   w i l d c a r d _ m a t c h ( s e l f ,   p a t t e r n ,   t e x t ) : 
 
                 " " " W i l d c a r d   p a t t e r n   m a t c h i n g " " " 
 
                 #   C o n v e r t   p a t t e r n   t o   r e g u l a r   e x p r e s s i o n 
 
                 #   C o n v e r t   *   t o   . *   a n d   e s c a p e   o t h e r   r e g e x   s p e c i a l   c h a r a c t e r s 
 
                 r e g e x _ p a t t e r n   =   r e . e s c a p e ( p a t t e r n ) . r e p l a c e ( r ' \ * ' ,   ' . * ' ) 
 
                 #   A d d   ^   a n d   $   f o r   e x a c t   m a t c h 
 
                 r e g e x _ p a t t e r n   =   f ' ^ { r e g e x _ p a t t e r n } $ ' 
 
 
 
                 t r y : 
 
                         r e t u r n   b o o l ( r e . m a t c h ( r e g e x _ p a t t e r n ,   t e x t ,   r e . I G N O R E C A S E ) ) 
 
                 e x c e p t   r e . e r r o r : 
 
                         #   I f   r e g e x   e r r o r ,   c o m p a r e   w i t h   e x a c t   m a t c h 
 
                         r e t u r n   p a t t e r n . l o w e r ( )   = =   t e x t . l o w e r ( ) 
 
 
 
         d e f   f i n d _ o b j e c t s _ b y _ p a t t e r n ( s e l f ,   p a t t e r n ) : 
 
                 " " " S e a r c h   o b j e c t s   b y   p a t t e r n   m a t c h i n g " " " 
 
                 i f   ' * '   n o t   i n   p a t t e r n : 
 
                         #   I f   n o   w i l d c a r d ,   e x a c t   m a t c h 
 
                         o b j   =   r t . g e t N o d e B y N a m e ( p a t t e r n ) 
 
                         r e t u r n   [ o b j ]   i f   o b j   e l s e   [ ] 
 
 
 
                 #   I f   w i l d c a r d   e x i s t s ,   p a t t e r n   m a t c h i n g 
 
                 m a t c h i n g _ o b j e c t s   =   [ ] 
 
                 t r y : 
 
                         f o r   o b j   i n   r t . o b j e c t s : 
 
                                 i f   o b j   a n d   h a s a t t r ( o b j ,   ' n a m e ' )   a n d   s e l f . w i l d c a r d _ m a t c h ( p a t t e r n ,   o b j . n a m e ) : 
 
                                         m a t c h i n g _ o b j e c t s . a p p e n d ( o b j ) 
 
                 e x c e p t   E x c e p t i o n   a s   e : 
 
                         p r i n t ( f " E r r o r   d u r i n g   o b j e c t   s e a r c h :   { s t r ( e ) } " ) 
 
 
 
                 r e t u r n   m a t c h i n g _ o b j e c t s 
 
         
 
         d e f   r e p l a c e _ o b j e c t s ( s e l f ) : 
 
                 " " " E x e c u t e   o b j e c t   r e p l a c e m e n t " " " 
 
                 i f   s e l f . t a b l e _ w i d g e t . r o w C o u n t ( )   = =   0 : 
 
                         Q M e s s a g e B o x . w a r n i n g ( s e l f ,   " E r r o r " ,   " N o   d a t a   t o   r e p l a c e " ) 
 
                         r e t u r n 
 
 
 
                 #   G e t   r e p l a c e m e n t   t y p e 
 
                 u s e _ i n s t a n c e   =   s e l f . i n s t a n c e _ r a d i o . i s C h e c k e d ( ) 
 
 
 
                 s u c c e s s _ c o u n t   =   0 
 
 
 
                 f o r   r o w   i n   r a n g e ( s e l f . t a b l e _ w i d g e t . r o w C o u n t ( ) ) : 
 
                         s o u r c e _ n a m e   =   s e l f . t a b l e _ w i d g e t . i t e m ( r o w ,   0 ) . t e x t ( ) . s t r i p ( ) 
 
                         t a r g e t _ n a m e   =   s e l f . t a b l e _ w i d g e t . i t e m ( r o w ,   1 ) . t e x t ( ) . s t r i p ( ) 
 
 
 
                         t r y : 
 
                                 #   S e a r c h   s o u r c e   o b j e c t s   ( w i l d c a r d   s u p p o r t ) 
 
                                 s o u r c e _ o b j e c t s   =   s e l f . f i n d _ o b j e c t s _ b y _ p a t t e r n ( s o u r c e _ n a m e ) 
 
                                 i f   n o t   s o u r c e _ o b j e c t s : 
 
                                         s e l f . t a b l e _ w i d g e t . s e t I t e m ( r o w ,   2 ,   Q T a b l e W i d g e t I t e m ( " N o   S o u r c e " ) ) 
 
                                         c o n t i n u e 
 
 
 
                                 #   S e a r c h   t a r g e t   o b j e c t s   ( w i l d c a r d   s u p p o r t ) 
 
                                 t a r g e t _ o b j e c t s   =   s e l f . f i n d _ o b j e c t s _ b y _ p a t t e r n ( t a r g e t _ n a m e ) 
 
                                 i f   n o t   t a r g e t _ o b j e c t s : 
 
                                         s e l f . t a b l e _ w i d g e t . s e t I t e m ( r o w ,   2 ,   Q T a b l e W i d g e t I t e m ( " N o   T a r g e t " ) ) 
 
                                         c o n t i n u e 
 
 
 
                                 #   I f   m u l t i p l e   s o u r c e   o b j e c t s ,   r e p l a c e   w i t h   f i r s t   t a r g e t 
 
                                 t a r g e t _ o b j   =   t a r g e t _ o b j e c t s [ 0 ] 
 
                                 r e p l a c e d _ c o u n t   =   0 
 
 
 
                                 f o r   s o u r c e _ o b j   i n   s o u r c e _ o b j e c t s : 
 
                                         #   E x e c u t e   r e p l a c e m e n t 
 
                                         i f   u s e _ i n s t a n c e : 
 
                                                 #   I n s t a n c e   r e p l a c e m e n t 
 
                                                 r t . i n s t a n c e R e p l a c e ( s o u r c e _ o b j ,   t a r g e t _ o b j ) 
 
                                         e l s e : 
 
                                                 #   R e f e r e n c e   r e p l a c e m e n t 
 
                                                 r t . r e f e r e n c e R e p l a c e ( s o u r c e _ o b j ,   t a r g e t _ o b j ) 
 
                                         r e p l a c e d _ c o u n t   + =   1 
 
 
 
                                 i f   r e p l a c e d _ c o u n t   >   1 : 
 
                                         s e l f . t a b l e _ w i d g e t . s e t I t e m ( r o w ,   2 ,   Q T a b l e W i d g e t I t e m ( f " S u c c e s s   ( { r e p l a c e d _ c o u n t }   o b j e c t s ) " ) ) 
 
                                 e l s e : 
 
                                         s e l f . t a b l e _ w i d g e t . s e t I t e m ( r o w ,   2 ,   Q T a b l e W i d g e t I t e m ( " S u c c e s s " ) ) 
 
                                 s u c c e s s _ c o u n t   + =   1 
 
 
 
                         e x c e p t   E x c e p t i o n   a s   e : 
 
                                 s e l f . t a b l e _ w i d g e t . s e t I t e m ( r o w ,   2 ,   Q T a b l e W i d g e t I t e m ( f " F a i l e d :   { s t r ( e ) } " ) ) 
 
 
 
                 #   C o m p l e t i o n   m e s s a g e 
 
                 Q M e s s a g e B o x . i n f o r m a t i o n ( 
 
                         s e l f , 
 
                         " C o m p l e t e " , 
 
                         f " R e p l a c e m e n t   p r o c e s s   c o m p l e t e d \ n S u c c e s s :   { s u c c e s s _ c o u n t }   /   T o t a l :   { s e l f . t a b l e _ w i d g e t . r o w C o u n t ( ) } " 
 
                 ) 
 
                 
 
         d e f   c l o s e _ w i n d o w ( s e l f ) : 
 
                 " " " C l o s e   w i n d o w " " " 
 
                 s e l f . c l o s e ( ) 
 
 
 
 d e f   s h o w _ o b j e c t _ r e p l a c e r ( ) : 
 
         " " " F u n c t i o n   t o   d i s p l a y   o b j e c t   r e p l a c e m e n t   t o o l " " " 
 
         #   C h e c k   e x i s t i n g   a p p l i c a t i o n   i n s t a n c e 
 
         a p p   =   Q A p p l i c a t i o n . i n s t a n c e ( ) 
 
         i f   a p p   i s   N o n e : 
 
                 a p p   =   Q A p p l i c a t i o n ( [ ] ) 
 
 
 
         #   C r e a t e   a n d   s h o w   t o o l   w i n d o w 
 
         r e p l a c e r   =   O b j e c t R e p l a c e r ( ) 
 
         r e p l a c e r . s h o w ( ) 
 
 
 
         r e t u r n   r e p l a c e r 
 
 
 
 #   T o o l   l a u n c h 
 
 #   W h e n   r u n n i n g   i n s i d e   3 d s   M a x 
 
 i f   _ _ n a m e _ _   = =   " _ _ m a i n _ _ " : 
 
         #   K e e p   a s   g l o b a l   v a r i a b l e   ( t o   p r e v e n t   g a r b a g e   c o l l e c t i o n ) 
 
         g l o b a l   o b j e c t _ r e p l a c e r _ t o o l 
 
         o b j e c t _ r e p l a c e r _ t o o l   =   s h o w _ o b j e c t _ r e p l a c e r ( ) 
 
 e l s e : 
 
         #   W h e n   r u n n i n g   a s   s c r i p t 
 
         s h o w _ o b j e c t _ r e p l a c e r ( ) 